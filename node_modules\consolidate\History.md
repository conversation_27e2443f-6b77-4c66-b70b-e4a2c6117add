0.15.1 / 2018-03-19
===================

 * add support for underscore partials
 * updating metadata and configurations
 * lint and cleanup code and tests

0.15.0 / 2017-11-01
===================

 * add plates support
 * add teacup support
 * add liquid-node support
 * add velocityjs support
 * allow absolute and relative partial paths
 * extend dot options
 * support layouts in vash

0.14.0 / 2016-01-24
===================

 * add slm support
 * add vash support
 * add twig support
 * fixes lodash 4 options
 * fixes liquid file extensions and path resolving

0.13.0 / 2015-05-26
===================

 * fixes react template error
 * adds promises when a callback function is not passed to `render`
 * documentation updates

0.11.0 / 2015-02-07
==================

 * fix aptl tests
 * update jade caching
 * add HTMLing support
 * add hamlet support
 * readme updates
 * add tinyliquid support
 * pass `options` to ECT.js
 * update ractive
 * update travis-ci to test 0.10, 0.12, and iojs

0.10.0 / 2013-11-23
==================

 * add lodash support
 * add nunjucks support

0.9.1 / 2013-04-29
==================

  * Update ECT version
  * Added support for Handlebars helpers with test.
  * Invalidates built-in dust cache if caching disabled

0.9.0 / 2013-03-28
==================

  * dust-helpers support, latest version of dust
  * Re-add doT - global leaks fixed
  * improving templayed support

0.8.0 / 2013-01-23
==================

  * add templayed support
  * add `then-jade` as an alternative to `jade`

0.7.0 / 2012-12-28
==================

  * add atpl support

0.6.0 2012-12-22
==================

  * add partials support
  * add support for toffee templates
  * remove dot it still leaks and the author has not fixed it

0.5.0 / 2012-10-29
==================

  * add `mote` support
  * add support to `dust` partials
  * add support for `ECT`
  * add support for rendering without file
  * add support for `JUST`
  * improve Haml-Coffee caching.

0.4.0 / 2012-07-30
==================

  * add doT support [sannis]
  * add mustache support [ForbesLindesay]
  * add walrus support [kagd]

0.3.1 / 2012-06-28
==================

  * add QEJS support
  * add underscore support
  * change whiskers to use pre-defined `.__express`
  * remove engines. Closes #37
  * remove kernel, cannot comply with our caching

0.3.0 / 2012-04-18
==================

  * Added partials loading for whiskers [gsf]
  * Added dustjs-linkedin support

0.2.0 / 2012-04-04
==================

  * Added support for dust [fatjonny]
  * Added handlebars support [jstewmon]

0.1.0 / 2012-01-03
==================

  * Added support for several more engines

0.0.1 / 2010-01-03
==================

  * Initial release
