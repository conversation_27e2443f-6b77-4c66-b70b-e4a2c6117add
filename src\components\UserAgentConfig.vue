<template>
  <div class="user-agent-config">
    <el-card>
      <div slot="header">
        <span>用户代理 (User-Agent) 配置</span>
      </div>
      
      <el-form :model="config" label-width="120px">
        <el-form-item label="启用用户代理">
          <el-switch v-model="config.enabled"></el-switch>
          <span class="help-text">启用后将覆盖请求的User-Agent头</span>
        </el-form-item>
        
        <el-form-item label="选择用户代理" v-if="config.enabled">
          <el-select v-model="config.selectedUA" placeholder="请选择用户代理" style="width: 100%">
            <el-option
              v-for="ua in userAgents"
              :key="ua.value"
              :label="ua.label"
              :value="ua.value">
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="自定义UA" v-if="config.enabled && config.selectedUA === 'custom'">
          <el-input
            v-model="config.customUA"
            type="textarea"
            :rows="3"
            placeholder="请输入自定义的User-Agent字符串">
          </el-input>
        </el-form-item>
        
        <el-form-item label="请求日语内容">
          <el-switch v-model="config.japanese"></el-switch>
          <span class="help-text">将Accept-Language设置为日语</span>
        </el-form-item>
      </el-form>
      
      <div class="preview-section" v-if="config.enabled">
        <h4>当前配置预览：</h4>
        <el-tag type="info">{{ getCurrentUA() }}</el-tag>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'UserAgentConfig',
  data() {
    return {
      config: {
        enabled: false,
        selectedUA: '',
        customUA: '',
        japanese: false
      },
      userAgents: [
        { label: 'Netscape 3', value: 'Mozilla/3.0 (Win95; I)' },
        { label: 'WinPhone 8.1', value: 'Mozilla/5.0 (Mobile; Windows Phone 8.1; Android 4.0; ARM; Trident/7.0; Touch; rv:11.0; IEMobile/11.0; NOKIA; Lumia 520) like iPhone OS 7_0_3 Mac OS X AppleWebKit/537 (KHTML, like Gecko) Mobile Safari/537' },
        { label: 'Safari 5 (Win7)', value: 'Mozilla/5.0 (Windows; U; Windows NT 6.1; en-US) AppleWebKit/533.21.1 (KHTML, like Gecko) Version/5.0.5 Safari/533.21.1' },
        { label: 'Safari 9 (Mac)', value: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_11) AppleWebKit/601.1.56 (KHTML, like Gecko) Version/9.0 Safari/601.1.56' },
        { label: 'iPad', value: 'Mozilla/5.0 (iPad; CPU OS 8_3 like Mac OS X) AppleWebKit/600.1.4 (KHTML, like Gecko) Version/8.0 Mobile/12F5027d Safari/600.1.4' },
        { label: 'iPhone 6', value: 'Mozilla/5.0 (iPhone; CPU iPhone OS 8_3 like Mac OS X) AppleWebKit/600.1.4 (KHTML, like Gecko) Version/8.0 Mobile/12F70 Safari/600.1.4' },
        { label: 'IE 6 (XP SP2)', value: 'Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1)' },
        { label: 'IE 7 (Vista)', value: 'Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.0; SLCC1)' },
        { label: 'IE 8 (Win2k3 x64)', value: 'Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.2; WOW64; Trident/4.0)' },
        { label: 'IE 8 (Win7)', value: 'Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; Trident/4.0)' },
        { label: 'IE 9 (Win7)', value: 'Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; Trident/5.0)' },
        { label: 'IE 10 (Win8)', value: 'Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.2; WOW64; Trident/6.0)' },
        { label: 'IE 11 (Surface2)', value: 'Mozilla/5.0 (Windows NT 6.3; ARM; Trident/7.0; Touch; rv:11.0) like Gecko' },
        { label: 'IE 11 (Win8.1)', value: 'Mozilla/5.0 (Windows NT 6.3; WOW64; Trident/7.0; rv:11.0) like Gecko' },
        { label: 'Edge (Win10)', value: 'Mozilla/5.0 (Windows NT 10.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.11082' },
        { label: 'Opera', value: 'Opera/9.80 (Windows NT 6.2; WOW64) Presto/2.12.388 Version/12.17' },
        { label: 'Firefox 3.6', value: 'Mozilla/5.0 (Windows; U; Windows NT 6.1; en-US; rv:1.9.2.7) Gecko/20100625 Firefox/3.6.7' },
        { label: 'Firefox 43', value: 'Mozilla/5.0 (Windows NT 6.3; WOW64; rv:43.0) Gecko/20100101 Firefox/43.0' },
        { label: 'Firefox Phone', value: 'Mozilla/5.0 (Mobile; rv:18.0) Gecko/18.0 Firefox/18.0' },
        { label: 'Firefox (Mac)', value: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.8; rv:24.0) Gecko/20100101 Firefox/24.0' },
        { label: 'Chrome (Win)', value: 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/48.0.2564.48 Safari/537.36' },
        { label: 'Chrome (Android)', value: 'Mozilla/5.0 (Linux; Android 5.1.1; Nexus 5 Build/LMY48B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/43.0.2357.78 Mobile Safari/537.36' },
        { label: 'ChromeBook', value: 'Mozilla/5.0 (X11; CrOS x86_64 6680.52.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2272.74 Safari/537.36' },
        { label: 'GoogleBot Crawler', value: 'Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)' },
        { label: 'Kindle Fire (Silk)', value: 'Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_6_3; en-us; Silk/1.0.22.79_10013310) AppleWebKit/533.16 (KHTML, like Gecko) Version/5.0 Safari/533.16 Silk-Accelerated=true' },
        { label: '自定义...', value: 'custom' }
      ]
    }
  },
  methods: {
    getConfig() {
      return { ...this.config }
    },
    
    resetConfig() {
      this.config = {
        enabled: false,
        selectedUA: '',
        customUA: '',
        japanese: false
      }
    },
    
    getCurrentUA() {
      if (!this.config.enabled) return '未启用'
      if (this.config.selectedUA === 'custom') {
        return this.config.customUA || '请输入自定义UA'
      }
      return this.config.selectedUA || '请选择用户代理'
    }
  }
}
</script>

<style scoped>
.user-agent-config {
  max-width: 800px;
}

.help-text {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}

.preview-section {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.preview-section h4 {
  margin: 0 0 10px 0;
  color: #606266;
}
</style>
