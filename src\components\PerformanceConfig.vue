<template>
  <div class="performance-config">
    <el-card>
      <div slot="header">
        <span>性能设置</span>
      </div>
      
      <el-form :model="config" label-width="150px">
        <el-form-item label="模拟调制解调器速度">
          <el-switch v-model="config.simulateModem"></el-switch>
          <span class="help-text">模拟56k调制解调器的网络延迟</span>
        </el-form-item>
        
        <el-form-item label="禁用缓存">
          <el-switch v-model="config.disableCaching"></el-switch>
          <span class="help-text">移除缓存相关头部，指定"no-cache"</span>
        </el-form-item>
        
        <el-form-item label="缓存总是新鲜">
          <el-switch v-model="config.alwaysFresh"></el-switch>
          <span class="help-text">强制缓存内容总是新鲜的</span>
        </el-form-item>
        
        <el-form-item label="隐藏304响应">
          <el-switch v-model="config.hide304s"></el-switch>
          <span class="help-text">在会话列表中隐藏304 Not Modified响应</span>
        </el-form-item>
        
        <el-form-item label="自动认证">
          <el-switch v-model="config.autoAuth"></el-switch>
          <span class="help-text">自动使用当前用户凭据响应认证挑战</span>
        </el-form-item>
      </el-form>
      
      <div class="performance-details" v-if="hasEnabledOptions">
        <h4>性能设置详情：</h4>
        <ul>
          <li v-if="config.simulateModem">
            <el-tag type="warning">调制解调器模拟</el-tag>
            <span>上传延迟: 300ms/KB, 下载延迟: 150ms/KB</span>
          </li>
          <li v-if="config.disableCaching">
            <el-tag type="info">禁用缓存</el-tag>
            <span>移除 If-None-Match 和 If-Modified-Since 头部</span>
          </li>
          <li v-if="config.alwaysFresh">
            <el-tag type="success">缓存新鲜</el-tag>
            <span>强制返回304响应给有条件请求</span>
          </li>
          <li v-if="config.hide304s">
            <el-tag>隐藏304</el-tag>
            <span>在UI中隐藏304 Not Modified响应</span>
          </li>
          <li v-if="config.autoAuth">
            <el-tag type="primary">自动认证</el-tag>
            <span>使用默认凭据自动响应认证</span>
          </li>
        </ul>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'PerformanceConfig',
  data() {
    return {
      config: {
        simulateModem: false,
        disableCaching: false,
        alwaysFresh: false,
        hide304s: false,
        autoAuth: false
      }
    }
  },
  computed: {
    hasEnabledOptions() {
      return Object.values(this.config).some(value => value === true)
    }
  },
  methods: {
    getConfig() {
      return { ...this.config }
    },
    
    resetConfig() {
      this.config = {
        simulateModem: false,
        disableCaching: false,
        alwaysFresh: false,
        hide304s: false,
        autoAuth: false
      }
    }
  }
}
</script>

<style scoped>
.performance-config {
  max-width: 800px;
}

.help-text {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}

.performance-details {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.performance-details h4 {
  margin: 0 0 15px 0;
  color: #606266;
}

.performance-details ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.performance-details li {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.performance-details li span {
  margin-left: 10px;
  color: #606266;
}
</style>
