<template>
  <div class="host-override-config">
    <el-card>
      <div slot="header">
        <span>主机重写规则</span>
      </div>
      
      <el-form :model="config" label-width="120px">
        <el-form-item label="启用主机重写">
          <el-switch v-model="config.enabled"></el-switch>
          <span class="help-text">启用后将重定向指定主机的请求到其他服务器</span>
        </el-form-item>
        
        <div v-if="config.enabled">
          <el-form-item label="重写规则">
            <el-button type="primary" size="small" @click="addRule">添加规则</el-button>
          </el-form-item>
          
          <div v-for="(rule, index) in config.rules" :key="index" class="rule-item">
            <el-row :gutter="10">
              <el-col :span="10">
                <el-input 
                  v-model="rule.originalHost" 
                  placeholder="原始主机名"
                  size="small">
                  <template slot="prepend">原主机</template>
                </el-input>
              </el-col>
              <el-col :span="10">
                <el-input 
                  v-model="rule.targetHost" 
                  placeholder="目标主机名"
                  size="small">
                  <template slot="prepend">目标主机</template>
                </el-input>
              </el-col>
              <el-col :span="4">
                <el-button 
                  type="danger" 
                  size="small" 
                  icon="el-icon-delete"
                  @click="removeRule(index)">
                </el-button>
              </el-col>
            </el-row>
            <div class="rule-example">
              <span class="example-label">示例:</span>
              <span class="example-text">
                {{ rule.originalHost ? `访问 "${rule.originalHost}" 时将连接到 "${rule.targetHost || '[请输入目标主机]'}"` : '请输入原始主机名' }}
              </span>
            </div>
          </div>
          
          <div v-if="config.rules.length === 0" class="no-rules">
            <el-alert
              title="暂无重写规则"
              description="点击"添加规则"按钮来创建主机重写规则"
              type="info"
              :closable="false">
            </el-alert>
          </div>
        </div>
      </el-form>
      
      <div class="rules-preview" v-if="config.enabled && config.rules.length > 0">
        <h4>重写规则预览：</h4>
        <el-table :data="validRules" size="small" border>
          <el-table-column prop="originalHost" label="原始主机" width="200"></el-table-column>
          <el-table-column prop="targetHost" label="目标主机" width="200"></el-table-column>
          <el-table-column label="说明">
            <template slot-scope="scope">
              <span>对 "{{ scope.row.originalHost }}" 的请求将被重定向到 "{{ scope.row.targetHost }}"</span>
            </template>
          </el-table-column>
        </el-table>
        
        <div class="usage-tips">
          <h5>使用说明：</h5>
          <ul>
            <li>原始主机名应该是完整的域名，如：api.example.com</li>
            <li>目标主机可以是IP地址或其他域名，如：************* 或 localhost:8080</li>
            <li>主机名匹配是不区分大小写的</li>
            <li>这对于本地开发和测试环境非常有用</li>
          </ul>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'HostOverrideConfig',
  data() {
    return {
      config: {
        enabled: false,
        rules: []
      }
    }
  },
  computed: {
    validRules() {
      return this.config.rules.filter(rule => 
        rule.originalHost && rule.originalHost.trim() && 
        rule.targetHost && rule.targetHost.trim()
      )
    }
  },
  methods: {
    getConfig() {
      return {
        enabled: this.config.enabled,
        rules: this.validRules
      }
    },
    
    resetConfig() {
      this.config = {
        enabled: false,
        rules: []
      }
    },
    
    addRule() {
      this.config.rules.push({
        originalHost: '',
        targetHost: ''
      })
    },
    
    removeRule(index) {
      this.config.rules.splice(index, 1)
    }
  },
  mounted() {
    // 默认添加一个空规则作为示例
    if (this.config.rules.length === 0) {
      this.addRule()
    }
  }
}
</script>

<style scoped>
.host-override-config {
  max-width: 900px;
}

.help-text {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}

.rule-item {
  margin-bottom: 15px;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;
}

.rule-example {
  margin-top: 8px;
  font-size: 12px;
}

.example-label {
  color: #909399;
  font-weight: bold;
}

.example-text {
  color: #606266;
  margin-left: 5px;
}

.no-rules {
  margin-top: 20px;
}

.rules-preview {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.rules-preview h4 {
  margin: 0 0 15px 0;
  color: #606266;
}

.usage-tips {
  margin-top: 15px;
  padding: 10px;
  background-color: #e6f7ff;
  border-radius: 4px;
}

.usage-tips h5 {
  margin: 0 0 10px 0;
  color: #1890ff;
}

.usage-tips ul {
  margin: 0;
  padding-left: 20px;
  color: #606266;
}

.usage-tips li {
  margin-bottom: 5px;
}
</style>
