class ScriptGenerator {
  static generate(config) {
    const script = this.generateBaseScript(config)
    return script
  }

  static generateBaseScript(config) {
    const timestamp = new Date().toLocaleString()
    
    let script = `import System;
import System.Windows.Forms;
import Fiddler;

// ========================================
// Fiddler 规则脚本 - 由可视化工具生成
// 生成时间: ${timestamp}
// ========================================

class Handlers
{
    // *****************
    // 规则选项变量
    // *****************
    
    ${this.generateRulesOptions(config)}
    
    // *****************
    // 用户代理配置
    // *****************
    
    ${this.generateUserAgentConfig(config.userAgent)}
    
    // *****************
    // 静态变量
    // *****************
    
    ${this.generateStaticVariables(config)}
    
    // *****************
    // 主要处理函数
    // *****************
    
    static function OnBeforeRequest(oSession: Session) {
        ${this.generateOnBeforeRequestBody(config)}
    }

    static function OnPeekAtResponseHeaders(oSession: Session) {
        ${this.generateOnPeekAtResponseHeadersBody(config)}
    }

    static function OnBeforeResponse(oSession: Session) {
        ${this.generateOnBeforeResponseBody(config)}
    }

    ${this.generateFilterFunctions(config.filters)}
    
    ${this.generateCustomMenus(config.others)}
    
    ${this.generateCustomColumns(config.others)}
    
    static function Main() {
        var today: Date = new Date();
        FiddlerObject.StatusText = " CustomRules.js was loaded at: " + today;
        
        ${this.generateMainBody(config.others)}
    }
    
    ${this.generateOnBootFunction(config.others)}
    
    ${this.generateOnExecAction(config)}
}`;

    return script
  }

  static generateRulesOptions(config) {
    let options = []
    
    // 性能选项
    if (config.performance) {
      if (config.performance.hide304s) {
        options.push(`public static RulesOption("Hide 304s")
    BindPref("fiddlerscript.rules.Hide304s")
    var m_Hide304s: boolean = true;`)
      }
      
      if (config.performance.simulateModem) {
        options.push(`public static RulesOption("Simulate &Modem Speeds", "Per&formance")
    var m_SimulateModem: boolean = true;`)
      }
      
      if (config.performance.disableCaching) {
        options.push(`public static RulesOption("&Disable Caching", "Per&formance")
    var m_DisableCaching: boolean = true;`)
      }
      
      if (config.performance.alwaysFresh) {
        options.push(`public static RulesOption("Cache Always &Fresh", "Per&formance")
    var m_AlwaysFresh: boolean = true;`)
      }
      
      if (config.performance.autoAuth) {
        options.push(`public static RulesOption("&Automatically Authenticate")
    BindPref("fiddlerscript.rules.AutoAuth")
    var m_AutoAuth: boolean = true;`)
      }
    }
    
    // 用户代理选项
    if (config.userAgent && config.userAgent.japanese) {
      options.push(`public static RulesOption("Request &Japanese Content")
    var m_Japanese: boolean = true;`)
    }
    
    return options.join('\n\n    ')
  }

  static generateUserAgentConfig(userAgentConfig) {
    if (!userAgentConfig || !userAgentConfig.enabled) {
      return 'public static var sUA: String = null;'
    }
    
    let ua = userAgentConfig.selectedUA
    if (userAgentConfig.selectedUA === 'custom') {
      ua = userAgentConfig.customUA || ''
    }
    
    return `public static var sUA: String = "${ua}";`
  }

  static generateStaticVariables(config) {
    let variables = []
    
    // 断点变量
    if (config.breakpoints) {
      const bp = config.breakpoints
      
      variables.push(`BindPref("fiddlerscript.ephemeral.bpRequestURI")
    public static var bpRequestURI:String = ${bp.requestURI.enabled && bp.requestURI.value ? `"${bp.requestURI.value}"` : 'null'};`)
      
      variables.push(`BindPref("fiddlerscript.ephemeral.bpResponseURI")
    public static var bpResponseURI:String = ${bp.responseURI.enabled && bp.responseURI.value ? `"${bp.responseURI.value}"` : 'null'};`)
      
      variables.push(`BindPref("fiddlerscript.ephemeral.bpMethod")
    public static var bpMethod: String = ${bp.method.enabled && bp.method.value ? `"${bp.method.value}"` : 'null'};`)
      
      variables.push(`static var bpStatus:int = ${bp.status.enabled && bp.status.value ? bp.status.value : '-1'};`)
      
      variables.push(`static var uiBoldURI: String = ${bp.boldURI.enabled && bp.boldURI.value ? `"${bp.boldURI.value}"` : 'null'};`)
    }
    
    // URL替换变量
    if (config.urlReplace && config.urlReplace.enabled && config.urlReplace.rules.length > 0) {
      const firstRule = config.urlReplace.rules[0]
      variables.push(`static var gs_ReplaceToken: String = "${firstRule.from}";`)
      variables.push(`static var gs_ReplaceTokenWith: String = "${firstRule.to}";`)
    } else {
      variables.push(`static var gs_ReplaceToken: String = null;`)
      variables.push(`static var gs_ReplaceTokenWith: String = null;`)
    }
    
    // 主机重写变量
    if (config.hostOverride && config.hostOverride.enabled && config.hostOverride.rules.length > 0) {
      const firstRule = config.hostOverride.rules[0]
      variables.push(`static var gs_OverridenHost: String = "${firstRule.originalHost.toLowerCase()}";`)
      variables.push(`static var gs_OverrideHostWith: String = "${firstRule.targetHost}";`)
    } else {
      variables.push(`static var gs_OverridenHost: String = null;`)
      variables.push(`static var gs_OverrideHostWith: String = null;`)
    }
    
    return variables.join('\n\n    ')
  }

  static generateOnBeforeRequestBody(config) {
    let body = []
    
    // URL替换逻辑
    body.push(`if ((null != gs_ReplaceToken) && (oSession.url.indexOf(gs_ReplaceToken)>-1)) {
            oSession.url = oSession.url.Replace(gs_ReplaceToken, gs_ReplaceTokenWith); 
        }`)
    
    // 主机重写逻辑
    body.push(`if ((null != gs_OverridenHost) && (oSession.host.toLowerCase() == gs_OverridenHost)) {
            oSession["x-overridehost"] = gs_OverrideHostWith; 
        }`)
    
    // 断点逻辑
    if (config.breakpoints) {
      body.push(`if ((null!=bpRequestURI) && oSession.uriContains(bpRequestURI)) {
            oSession["x-breakrequest"]="uri";
        }`)
      
      body.push(`if ((null!=bpMethod) && (oSession.HTTPMethodIs(bpMethod))) {
            oSession["x-breakrequest"]="method";
        }`)
      
      body.push(`if ((null!=uiBoldURI) && oSession.uriContains(uiBoldURI)) {
            oSession["ui-bold"]="QuickExec";
        }`)
    }
    
    // 性能设置
    if (config.performance) {
      if (config.performance.simulateModem) {
        body.push(`if (m_SimulateModem) {
            oSession["request-trickle-delay"] = "300"; 
            oSession["response-trickle-delay"] = "150"; 
        }`)
      }
      
      if (config.performance.disableCaching) {
        body.push(`if (m_DisableCaching) {
            oSession.oRequest.headers.Remove("If-None-Match");
            oSession.oRequest.headers.Remove("If-Modified-Since");
            oSession.oRequest["Pragma"] = "no-cache";
        }`)
      }
      
      if (config.performance.autoAuth) {
        body.push(`if (m_AutoAuth) {
            oSession["X-AutoAuth"] = "(default)";
        }`)
      }
      
      if (config.performance.alwaysFresh) {
        body.push(`if (m_AlwaysFresh && (oSession.oRequest.headers.Exists("If-Modified-Since") || oSession.oRequest.headers.Exists("If-None-Match"))) {
            oSession.utilCreateResponseAndBypassServer();
            oSession.responseCode = 304;
            oSession["ui-backcolor"] = "Lavender";
        }`)
      }
    }
    
    // 用户代理设置
    if (config.userAgent) {
      body.push(`if (null != sUA) {
            oSession.oRequest["User-Agent"] = sUA; 
        }`)
      
      if (config.userAgent.japanese) {
        body.push(`if (m_Japanese) {
            oSession.oRequest["Accept-Language"] = "ja";
        }`)
      }
    }
    
    return body.join('\n\n        ')
  }

  static generateOnPeekAtResponseHeadersBody(config) {
    let body = []
    
    if (config.performance && config.performance.disableCaching) {
      body.push(`if (m_DisableCaching) {
            oSession.oResponse.headers.Remove("Expires");
            oSession.oResponse["Cache-Control"] = "no-cache";
        }`)
    }
    
    if (config.breakpoints) {
      body.push(`if ((bpStatus>0) && (oSession.responseCode == bpStatus)) {
            oSession["x-breakresponse"]="status";
            oSession.bBufferResponse = true;
        }`)
      
      body.push(`if ((null!=bpResponseURI) && oSession.uriContains(bpResponseURI)) {
            oSession["x-breakresponse"]="uri";
            oSession.bBufferResponse = true;
        }`)
    }
    
    return body.join('\n\n        ')
  }

  static generateOnBeforeResponseBody(config) {
    let body = []

    if (config.performance && config.performance.hide304s) {
      body.push(`if (m_Hide304s && oSession.responseCode == 304) {
            oSession["ui-hide"] = "true";
        }`)
    }

    // 添加过滤函数调用
    if (config.filters) {
      if (config.filters.urlFilter && config.filters.urlFilter.enabled) {
        body.push('ApplyUrlFilter(oSession);')
      }
      if (config.filters.fileTypeFilter && config.filters.fileTypeFilter.enabled) {
        body.push('ApplyFileTypeFilter(oSession);')
      }
      if (config.filters.sizeFilter && config.filters.sizeFilter.enabled) {
        body.push('ApplySizeFilter(oSession);')
      }
      if (config.filters.statusFilter && config.filters.statusFilter.enabled) {
        body.push('ApplyStatusFilter(oSession);')
      }
      if (config.filters.processFilter && config.filters.processFilter.enabled) {
        body.push('ApplyProcessFilter(oSession);')
      }
    }

    return body.join('\n\n        ')
  }

  static generateCustomMenus(othersConfig) {
    if (!othersConfig || !othersConfig.customMenu || !othersConfig.customMenu.enabled) {
      return ''
    }
    
    const menu = othersConfig.customMenu
    if (!menu.items || menu.items.length === 0) {
      return ''
    }
    
    let menuCode = `QuickLinkMenu("${menu.name || '自定义菜单'}")\n`
    
    menu.items.forEach(item => {
      if (item.title && item.url) {
        menuCode += `    QuickLinkItem("${item.title}", "${item.url}")\n`
      }
    })
    
    menuCode += `    public static function DoLinksMenu(sText: String, sAction: String) {
        Utilities.LaunchHyperlink(sAction);
    }`
    
    return menuCode
  }

  static generateCustomColumns(othersConfig) {
    if (!othersConfig || !othersConfig.customColumn || !othersConfig.customColumn.enabled) {
      return ''
    }
    
    const column = othersConfig.customColumn
    if (!column.name || !column.content) {
      return ''
    }
    
    return `public static BindUIColumn("${column.name}", ${column.width || 100})
    function Fill${column.name}Column(oS: Session): String {
        return oS.oResponse["${column.content.replace('@response.', '').replace('@request.', '')}"];
    }`
  }

  static generateMainBody(othersConfig) {
    let body = []
    
    if (othersConfig && othersConfig.customColumn && othersConfig.customColumn.enabled) {
      const column = othersConfig.customColumn
      if (column.name && column.content) {
        body.push(`// 添加自定义列`)
        body.push(`UI.lvSessions.AddBoundColumn("${column.name}", ${column.width || 100}, "${column.content}");`)
      }
    }
    
    if (othersConfig && othersConfig.hotkey && othersConfig.hotkey.enabled) {
      const hotkey = othersConfig.hotkey
      if (hotkey.modifier && hotkey.key && hotkey.action) {
        body.push(`// 注册全局热键`)
        body.push(`UI.RegisterCustomHotkey(HotkeyModifiers.${hotkey.modifier}, Keys.${hotkey.key.toUpperCase()}, "${hotkey.action}");`)
      }
    }
    
    return body.join('\n        ')
  }

  static generateOnBootFunction(othersConfig) {
    if (!othersConfig || !othersConfig.bootMessage || !othersConfig.bootMessage.enabled) {
      return ''
    }
    
    const message = othersConfig.bootMessage.message
    if (!message) {
      return ''
    }
    
    return `static function OnBoot() {
        MessageBox.Show("${message}");
    }`
  }

  static generateOnExecAction(config) {
    // 这里可以根据配置生成自定义的QuickExec命令
    // 暂时返回基本的OnExecAction函数
    return `static function OnExecAction(sParams: String[]): Boolean {
        FiddlerObject.StatusText = "ExecAction: " + sParams[0];
        var sAction = sParams[0].toLowerCase();

        // 在这里可以添加自定义命令

        return false;
    }`
  }

  static generateFilterFunctions(filtersConfig) {
    if (!filtersConfig) return ''

    let functions = []

    // URL过滤函数
    if (filtersConfig.urlFilter && filtersConfig.urlFilter.enabled) {
      functions.push(this.generateUrlFilterFunction(filtersConfig.urlFilter))
    }

    // 文件类型过滤函数
    if (filtersConfig.fileTypeFilter && filtersConfig.fileTypeFilter.enabled) {
      functions.push(this.generateFileTypeFilterFunction(filtersConfig.fileTypeFilter))
    }

    // 响应大小过滤函数
    if (filtersConfig.sizeFilter && filtersConfig.sizeFilter.enabled) {
      functions.push(this.generateSizeFilterFunction(filtersConfig.sizeFilter))
    }

    // 状态码过滤函数
    if (filtersConfig.statusFilter && filtersConfig.statusFilter.enabled) {
      functions.push(this.generateStatusFilterFunction(filtersConfig.statusFilter))
    }

    // 进程过滤函数
    if (filtersConfig.processFilter && filtersConfig.processFilter.enabled) {
      functions.push(this.generateProcessFilterFunction(filtersConfig.processFilter))
    }

    return functions.join('\n\n    ')
  }

  static generateUrlFilterFunction(urlFilter) {
    let conditions = []

    urlFilter.rules.forEach((rule, index) => {
      if (!rule.pattern) return

      let condition = ''
      switch (rule.type) {
        case 'contains':
          condition = `oSession.uriContains("${rule.pattern}")`
          break
        case 'startsWith':
          condition = `oSession.url.StartsWith("${rule.pattern}")`
          break
        case 'endsWith':
          condition = `oSession.url.EndsWith("${rule.pattern}")`
          break
        case 'regex':
          condition = `System.Text.RegularExpressions.Regex.IsMatch(oSession.url, "${rule.pattern.replace(/\\/g, '\\\\')}")`
          break
      }

      if (condition) {
        conditions.push(condition)
      }
    })

    if (conditions.length === 0) return ''

    const combinedCondition = conditions.join(' || ')
    const action = urlFilter.mode === 'hide' ? 'oSession["ui-hide"] = "url-filter";' : 'if (!(' + combinedCondition + ')) { oSession["ui-hide"] = "url-filter"; }'

    return `// URL过滤规则
    static function ApplyUrlFilter(oSession: Session) {
        if (${combinedCondition}) {
            ${urlFilter.mode === 'hide' ? 'oSession["ui-hide"] = "url-filter";' : '// 显示匹配的URL'}
        }${urlFilter.mode === 'show' ? ' else { oSession["ui-hide"] = "url-filter"; }' : ''}
    }`
  }

  static generateFileTypeFilterFunction(fileTypeFilter) {
    let extensions = []

    // 预定义文件类型
    const typeMap = {
      css: ['.css'],
      js: ['.js', '.jsx', '.ts', '.tsx'],
      image: ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg', '.webp', '.ico'],
      font: ['.woff', '.woff2', '.ttf', '.eot', '.otf'],
      video: ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm'],
      audio: ['.mp3', '.wav', '.ogg', '.aac', '.flac'],
      document: ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx']
    }

    fileTypeFilter.types.forEach(type => {
      if (typeMap[type]) {
        extensions.push(...typeMap[type])
      }
    })

    // 自定义扩展名
    if (fileTypeFilter.customExtensions) {
      const customExts = fileTypeFilter.customExtensions.split(',').map(ext => ext.trim())
      extensions.push(...customExts)
    }

    if (extensions.length === 0) return ''

    const conditions = extensions.map(ext => `oSession.url.EndsWith("${ext}")`).join(' || ')

    return `// 文件类型过滤规则
    static function ApplyFileTypeFilter(oSession: Session) {
        if (${conditions}) {
            ${fileTypeFilter.mode === 'hide' ? 'oSession["ui-hide"] = "filetype-filter";' : '// 显示匹配的文件类型'}
        }${fileTypeFilter.mode === 'show' ? ' else { oSession["ui-hide"] = "filetype-filter"; }' : ''}
    }`
  }

  static generateSizeFilterFunction(sizeFilter) {
    let conditions = []

    if (sizeFilter.hideZeroSize) {
      conditions.push('oSession.responseBodyBytes.Length == 0')
    }

    if (sizeFilter.minSizeEnabled) {
      conditions.push(`oSession.responseBodyBytes.Length < ${sizeFilter.minSize}`)
    }

    if (sizeFilter.maxSizeEnabled) {
      conditions.push(`oSession.responseBodyBytes.Length > ${sizeFilter.maxSize}`)
    }

    if (conditions.length === 0) return ''

    const combinedCondition = conditions.join(' || ')

    return `// 响应大小过滤规则
    static function ApplySizeFilter(oSession: Session) {
        if (${combinedCondition}) {
            oSession["ui-hide"] = "size-filter";
        }
    }`
  }

  static generateStatusFilterFunction(statusFilter) {
    let codes = [...statusFilter.codes]

    // 自定义状态码
    if (statusFilter.customCodes) {
      const customCodes = statusFilter.customCodes.split(',').map(code => code.trim())
      codes.push(...customCodes)
    }

    if (codes.length === 0) return ''

    const conditions = codes.map(code => `oSession.responseCode == ${code}`).join(' || ')

    return `// HTTP状态码过滤规则
    static function ApplyStatusFilter(oSession: Session) {
        if (${conditions}) {
            ${statusFilter.mode === 'hide' ? 'oSession["ui-hide"] = "status-filter";' : '// 显示匹配的状态码'}
        }${statusFilter.mode === 'show' ? ' else { oSession["ui-hide"] = "status-filter"; }' : ''}
    }`
  }

  static generateProcessFilterFunction(processFilter) {
    if (!processFilter.processNames) return ''

    const processes = processFilter.processNames.split(',').map(name => name.trim().toLowerCase())
    const conditions = processes.map(proc => `sProc.Contains("${proc}")`).join(' || ')

    return `// 进程过滤规则
    static function ApplyProcessFilter(oSession: Session) {
        var sProc = ("" + oSession["x-ProcessInfo"]).ToLower();
        if (${conditions}) {
            ${processFilter.mode === 'hide' ? 'oSession["ui-hide"] = "process-filter";' : '// 显示匹配的进程'}
        }${processFilter.mode === 'show' ? ' else { oSession["ui-hide"] = "process-filter"; }' : ''}
    }`
  }
}

export default ScriptGenerator
