<template>
  <div id="app">
    <el-container>
      <el-header>
        <h1>Fiddler 规则脚本生成器</h1>
        <p>可视化配置Fiddler规则，动态生成FiddlerScript脚本文件</p>
      </el-header>
      
      <el-main>
        <el-tabs v-model="activeTab" type="card">
          <!-- 用户代理配置 -->
          <el-tab-pane label="用户代理" name="userAgent">
            <UserAgentConfig ref="userAgent" />
          </el-tab-pane>
          
          <!-- 性能设置 -->
          <el-tab-pane label="性能设置" name="performance">
            <PerformanceConfig ref="performance" />
          </el-tab-pane>
          
          <!-- 断点设置 -->
          <el-tab-pane label="断点设置" name="breakpoints">
            <BreakpointConfig ref="breakpoints" />
          </el-tab-pane>
          
          <!-- URL替换 -->
          <el-tab-pane label="URL替换" name="urlReplace">
            <UrlReplaceConfig ref="urlReplace" />
          </el-tab-pane>
          
          <!-- 主机重写 -->
          <el-tab-pane label="主机重写" name="hostOverride">
            <HostOverrideConfig ref="hostOverride" />
          </el-tab-pane>
          
          <!-- 过滤规则 -->
          <el-tab-pane label="过滤规则" name="filters">
            <FilterConfig ref="filters" />
          </el-tab-pane>

          <!-- 其他选项 -->
          <el-tab-pane label="其他选项" name="others">
            <OtherConfig ref="others" />
          </el-tab-pane>

          <!-- 脚本预览 -->
          <el-tab-pane label="脚本预览" name="preview">
            <ScriptPreview :config="allConfig" />
          </el-tab-pane>
        </el-tabs>
        
        <div class="action-buttons">
          <el-button type="primary" @click="generateScript" size="large">
            生成脚本
          </el-button>
          <el-button @click="downloadScript" size="large" :disabled="!generatedScript">
            下载脚本
          </el-button>
          <el-button @click="resetConfig" size="large">
            重置配置
          </el-button>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script>
import UserAgentConfig from './components/UserAgentConfig.vue'
import PerformanceConfig from './components/PerformanceConfig.vue'
import BreakpointConfig from './components/BreakpointConfig.vue'
import UrlReplaceConfig from './components/UrlReplaceConfig.vue'
import HostOverrideConfig from './components/HostOverrideConfig.vue'
import FilterConfig from './components/FilterConfig.vue'
import OtherConfig from './components/OtherConfig.vue'
import ScriptPreview from './components/ScriptPreview.vue'
import ScriptGenerator from './utils/ScriptGenerator'

export default {
  name: 'App',
  components: {
    UserAgentConfig,
    PerformanceConfig,
    BreakpointConfig,
    UrlReplaceConfig,
    HostOverrideConfig,
    FilterConfig,
    OtherConfig,
    ScriptPreview
  },
  data() {
    return {
      activeTab: 'userAgent',
      generatedScript: ''
    }
  },
  computed: {
    allConfig() {
      return {
        userAgent: this.$refs.userAgent?.getConfig() || {},
        performance: this.$refs.performance?.getConfig() || {},
        breakpoints: this.$refs.breakpoints?.getConfig() || {},
        urlReplace: this.$refs.urlReplace?.getConfig() || {},
        hostOverride: this.$refs.hostOverride?.getConfig() || {},
        filters: this.$refs.filters?.getConfig() || {},
        others: this.$refs.others?.getConfig() || {}
      }
    }
  },
  methods: {
    generateScript() {
      try {
        this.generatedScript = ScriptGenerator.generate(this.allConfig)
        this.$message.success('脚本生成成功！')
        this.activeTab = 'preview'
      } catch (error) {
        this.$message.error('脚本生成失败：' + error.message)
      }
    },
    
    downloadScript() {
      if (!this.generatedScript) {
        this.$message.warning('请先生成脚本')
        return
      }
      
      const blob = new Blob([this.generatedScript], { type: 'text/javascript' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = 'CustomRules.js'
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
      
      this.$message.success('脚本下载成功！')
    },
    
    resetConfig() {
      this.$confirm('确定要重置所有配置吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 重置所有组件的配置
        Object.keys(this.$refs).forEach(key => {
          if (this.$refs[key] && typeof this.$refs[key].resetConfig === 'function') {
            this.$refs[key].resetConfig()
          }
        })
        this.generatedScript = ''
        this.$message.success('配置已重置')
      }).catch(() => {
        // 用户取消
      })
    }
  }
}
</script>

<style>
#app {
  min-height: 100vh;
}

.el-header {
  background-color: #409EFF;
  color: white;
  text-align: center;
  padding: 20px;
}

.el-header h1 {
  margin: 0 0 10px 0;
  font-size: 28px;
}

.el-header p {
  margin: 0;
  font-size: 14px;
  opacity: 0.9;
}

.el-main {
  padding: 20px;
}

.action-buttons {
  text-align: center;
  margin-top: 30px;
  padding: 20px;
  border-top: 1px solid #eee;
}

.action-buttons .el-button {
  margin: 0 10px;
}
</style>
