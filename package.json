{"name": "fiddlerscript", "version": "1.0.0", "main": "SampleRules.js", "scripts": {"dev": "webpack serve --mode development", "build": "webpack --mode production", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "Fiddler规则脚本可视化生成器", "dependencies": {"@babel/core": "^7.27.4", "@babel/preset-env": "^7.27.2", "babel-loader": "^10.0.0", "css-loader": "^7.1.2", "element-ui": "^2.15.14", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.6.3", "url-loader": "^4.1.1", "vue": "^2.7.16", "vue-template-compiler": "^2.7.16", "webpack": "^5.99.9", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.2"}, "devDependencies": {"vue-loader": "^15.10.0", "vue-style-loader": "^4.1.3"}}