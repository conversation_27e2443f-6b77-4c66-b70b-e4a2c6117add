<template>
  <div class="filter-config">
    <el-card>
      <div slot="header">
        <span>过滤规则配置</span>
      </div>
      
      <el-form :model="config" label-width="150px">
        <!-- URL过滤 -->
        <el-card class="filter-section">
          <div slot="header">URL过滤</div>
          
          <el-form-item label="启用URL过滤">
            <el-switch v-model="config.urlFilter.enabled"></el-switch>
          </el-form-item>
          
          <div v-if="config.urlFilter.enabled">
            <el-form-item label="过滤模式">
              <el-radio-group v-model="config.urlFilter.mode">
                <el-radio label="hide">隐藏匹配的URL</el-radio>
                <el-radio label="show">只显示匹配的URL</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item label="URL规则">
              <el-button type="primary" size="small" @click="addUrlRule">添加规则</el-button>
            </el-form-item>
            
            <div v-for="(rule, index) in config.urlFilter.rules" :key="index" class="rule-item">
              <el-row :gutter="10">
                <el-col :span="6">
                  <el-select v-model="rule.type" placeholder="匹配类型" size="small">
                    <el-option label="包含" value="contains"></el-option>
                    <el-option label="开始于" value="startsWith"></el-option>
                    <el-option label="结束于" value="endsWith"></el-option>
                    <el-option label="正则表达式" value="regex"></el-option>
                  </el-select>
                </el-col>
                <el-col :span="14">
                  <el-input 
                    v-model="rule.pattern" 
                    :placeholder="getUrlPlaceholder(rule.type)"
                    size="small">
                  </el-input>
                </el-col>
                <el-col :span="4">
                  <el-button 
                    type="danger" 
                    size="small" 
                    icon="el-icon-delete"
                    @click="removeUrlRule(index)">
                  </el-button>
                </el-col>
              </el-row>
            </div>
          </div>
        </el-card>
        
        <!-- 文件类型过滤 -->
        <el-card class="filter-section">
          <div slot="header">文件类型过滤</div>
          
          <el-form-item label="启用文件类型过滤">
            <el-switch v-model="config.fileTypeFilter.enabled"></el-switch>
          </el-form-item>
          
          <div v-if="config.fileTypeFilter.enabled">
            <el-form-item label="过滤模式">
              <el-radio-group v-model="config.fileTypeFilter.mode">
                <el-radio label="hide">隐藏指定类型</el-radio>
                <el-radio label="show">只显示指定类型</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item label="文件类型">
              <el-checkbox-group v-model="config.fileTypeFilter.types">
                <el-checkbox label="css">CSS文件</el-checkbox>
                <el-checkbox label="js">JavaScript文件</el-checkbox>
                <el-checkbox label="image">图片文件</el-checkbox>
                <el-checkbox label="font">字体文件</el-checkbox>
                <el-checkbox label="video">视频文件</el-checkbox>
                <el-checkbox label="audio">音频文件</el-checkbox>
                <el-checkbox label="document">文档文件</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            
            <el-form-item label="自定义扩展名">
              <el-input 
                v-model="config.fileTypeFilter.customExtensions" 
                placeholder="例如: .xml,.json,.txt (用逗号分隔)"
                size="small">
              </el-input>
            </el-form-item>
          </div>
        </el-card>
        
        <!-- 响应大小过滤 -->
        <el-card class="filter-section">
          <div slot="header">响应大小过滤</div>
          
          <el-form-item label="启用大小过滤">
            <el-switch v-model="config.sizeFilter.enabled"></el-switch>
          </el-form-item>
          
          <div v-if="config.sizeFilter.enabled">
            <el-form-item label="过滤零字节响应">
              <el-switch v-model="config.sizeFilter.hideZeroSize"></el-switch>
              <span class="help-text">隐藏响应大小为0的请求</span>
            </el-form-item>
            
            <el-form-item label="最小大小过滤">
              <el-switch v-model="config.sizeFilter.minSizeEnabled"></el-switch>
            </el-form-item>
            <el-form-item v-if="config.sizeFilter.minSizeEnabled" label="最小大小">
              <el-input-number 
                v-model="config.sizeFilter.minSize" 
                :min="0"
                size="small">
              </el-input-number>
              <span class="help-text">字节</span>
            </el-form-item>
            
            <el-form-item label="最大大小过滤">
              <el-switch v-model="config.sizeFilter.maxSizeEnabled"></el-switch>
            </el-form-item>
            <el-form-item v-if="config.sizeFilter.maxSizeEnabled" label="最大大小">
              <el-input-number 
                v-model="config.sizeFilter.maxSize" 
                :min="0"
                size="small">
              </el-input-number>
              <span class="help-text">字节</span>
            </el-form-item>
          </div>
        </el-card>
        
        <!-- HTTP状态码过滤 -->
        <el-card class="filter-section">
          <div slot="header">HTTP状态码过滤</div>
          
          <el-form-item label="启用状态码过滤">
            <el-switch v-model="config.statusFilter.enabled"></el-switch>
          </el-form-item>
          
          <div v-if="config.statusFilter.enabled">
            <el-form-item label="过滤模式">
              <el-radio-group v-model="config.statusFilter.mode">
                <el-radio label="hide">隐藏指定状态码</el-radio>
                <el-radio label="show">只显示指定状态码</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item label="状态码">
              <el-checkbox-group v-model="config.statusFilter.codes">
                <el-checkbox label="200">200 OK</el-checkbox>
                <el-checkbox label="301">301 Moved Permanently</el-checkbox>
                <el-checkbox label="302">302 Found</el-checkbox>
                <el-checkbox label="304">304 Not Modified</el-checkbox>
                <el-checkbox label="400">400 Bad Request</el-checkbox>
                <el-checkbox label="401">401 Unauthorized</el-checkbox>
                <el-checkbox label="403">403 Forbidden</el-checkbox>
                <el-checkbox label="404">404 Not Found</el-checkbox>
                <el-checkbox label="500">500 Internal Server Error</el-checkbox>
                <el-checkbox label="502">502 Bad Gateway</el-checkbox>
                <el-checkbox label="503">503 Service Unavailable</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            
            <el-form-item label="自定义状态码">
              <el-input 
                v-model="config.statusFilter.customCodes" 
                placeholder="例如: 418,429,503 (用逗号分隔)"
                size="small">
              </el-input>
            </el-form-item>
          </div>
        </el-card>
        
        <!-- 进程过滤 -->
        <el-card class="filter-section">
          <div slot="header">进程过滤</div>
          
          <el-form-item label="启用进程过滤">
            <el-switch v-model="config.processFilter.enabled"></el-switch>
          </el-form-item>
          
          <div v-if="config.processFilter.enabled">
            <el-form-item label="过滤模式">
              <el-radio-group v-model="config.processFilter.mode">
                <el-radio label="hide">隐藏指定进程</el-radio>
                <el-radio label="show">只显示指定进程</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item label="进程名称">
              <el-input 
                v-model="config.processFilter.processNames" 
                placeholder="例如: chrome.exe,firefox.exe (用逗号分隔)"
                size="small">
              </el-input>
              <span class="help-text">不区分大小写</span>
            </el-form-item>
          </div>
        </el-card>
      </el-form>
      
      <div class="filter-summary" v-if="hasEnabledFilters">
        <h4>当前过滤规则摘要：</h4>
        <ul>
          <li v-if="config.urlFilter.enabled">
            <el-tag type="primary">URL过滤</el-tag>
            <span>{{ config.urlFilter.mode === 'hide' ? '隐藏' : '显示' }} {{ config.urlFilter.rules.length }} 个URL规则</span>
          </li>
          <li v-if="config.fileTypeFilter.enabled">
            <el-tag type="success">文件类型</el-tag>
            <span>{{ config.fileTypeFilter.mode === 'hide' ? '隐藏' : '显示' }} {{ config.fileTypeFilter.types.length }} 种文件类型</span>
          </li>
          <li v-if="config.sizeFilter.enabled">
            <el-tag type="warning">响应大小</el-tag>
            <span>{{ getSizeFilterSummary() }}</span>
          </li>
          <li v-if="config.statusFilter.enabled">
            <el-tag type="info">状态码</el-tag>
            <span>{{ config.statusFilter.mode === 'hide' ? '隐藏' : '显示' }} {{ config.statusFilter.codes.length }} 个状态码</span>
          </li>
          <li v-if="config.processFilter.enabled">
            <el-tag type="danger">进程</el-tag>
            <span>{{ config.processFilter.mode === 'hide' ? '隐藏' : '显示' }} 指定进程的请求</span>
          </li>
        </ul>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'FilterConfig',
  data() {
    return {
      config: {
        urlFilter: {
          enabled: false,
          mode: 'hide', // hide | show
          rules: []
        },
        fileTypeFilter: {
          enabled: false,
          mode: 'hide', // hide | show
          types: [],
          customExtensions: ''
        },
        sizeFilter: {
          enabled: false,
          hideZeroSize: false,
          minSizeEnabled: false,
          minSize: 1024,
          maxSizeEnabled: false,
          maxSize: 1048576
        },
        statusFilter: {
          enabled: false,
          mode: 'hide', // hide | show
          codes: [],
          customCodes: ''
        },
        processFilter: {
          enabled: false,
          mode: 'show', // hide | show
          processNames: ''
        }
      }
    }
  },
  computed: {
    hasEnabledFilters() {
      return Object.values(this.config).some(filter => filter.enabled)
    }
  },
  methods: {
    getConfig() {
      return JSON.parse(JSON.stringify(this.config))
    },
    
    resetConfig() {
      this.config = {
        urlFilter: { enabled: false, mode: 'hide', rules: [] },
        fileTypeFilter: { enabled: false, mode: 'hide', types: [], customExtensions: '' },
        sizeFilter: { enabled: false, hideZeroSize: false, minSizeEnabled: false, minSize: 1024, maxSizeEnabled: false, maxSize: 1048576 },
        statusFilter: { enabled: false, mode: 'hide', codes: [], customCodes: '' },
        processFilter: { enabled: false, mode: 'show', processNames: '' }
      }
    },
    
    addUrlRule() {
      this.config.urlFilter.rules.push({
        type: 'contains',
        pattern: ''
      })
    },
    
    removeUrlRule(index) {
      this.config.urlFilter.rules.splice(index, 1)
    },
    
    getUrlPlaceholder(type) {
      const placeholders = {
        contains: '例如: /api/',
        startsWith: '例如: https://api.example.com',
        endsWith: '例如: .css',
        regex: '例如: \\.(css|js)$'
      }
      return placeholders[type] || '输入匹配模式'
    },
    
    getSizeFilterSummary() {
      let summary = []
      if (this.config.sizeFilter.hideZeroSize) {
        summary.push('隐藏零字节')
      }
      if (this.config.sizeFilter.minSizeEnabled) {
        summary.push(`最小${this.config.sizeFilter.minSize}字节`)
      }
      if (this.config.sizeFilter.maxSizeEnabled) {
        summary.push(`最大${this.config.sizeFilter.maxSize}字节`)
      }
      return summary.join(', ') || '已启用'
    }
  },
  mounted() {
    // 默认添加一个URL规则示例
    if (this.config.urlFilter.rules.length === 0) {
      this.addUrlRule()
    }
  }
}
</script>

<style scoped>
.filter-config {
  max-width: 900px;
}

.filter-section {
  margin-bottom: 20px;
}

.help-text {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}

.rule-item {
  margin-bottom: 10px;
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;
}

.filter-summary {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.filter-summary h4 {
  margin: 0 0 15px 0;
  color: #606266;
}

.filter-summary ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.filter-summary li {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.filter-summary li span {
  margin-left: 10px;
  color: #606266;
}
</style>
