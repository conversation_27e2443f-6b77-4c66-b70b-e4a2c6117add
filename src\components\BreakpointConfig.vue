<template>
  <div class="breakpoint-config">
    <el-card>
      <div slot="header">
        <span>断点设置</span>
      </div>
      
      <el-form :model="config" label-width="150px">
        <!-- 请求URI断点 -->
        <el-form-item label="请求URI断点">
          <el-switch v-model="config.requestURI.enabled"></el-switch>
        </el-form-item>
        <el-form-item v-if="config.requestURI.enabled" label="URI包含">
          <el-input 
            v-model="config.requestURI.value" 
            placeholder="例如: /api/users"
            clearable>
          </el-input>
          <span class="help-text">当请求URI包含此字符串时触发断点</span>
        </el-form-item>
        
        <!-- 响应URI断点 -->
        <el-form-item label="响应URI断点">
          <el-switch v-model="config.responseURI.enabled"></el-switch>
        </el-form-item>
        <el-form-item v-if="config.responseURI.enabled" label="URI包含">
          <el-input 
            v-model="config.responseURI.value" 
            placeholder="例如: /api/data"
            clearable>
          </el-input>
          <span class="help-text">当响应URI包含此字符串时触发断点</span>
        </el-form-item>
        
        <!-- HTTP方法断点 -->
        <el-form-item label="HTTP方法断点">
          <el-switch v-model="config.method.enabled"></el-switch>
        </el-form-item>
        <el-form-item v-if="config.method.enabled" label="HTTP方法">
          <el-select v-model="config.method.value" placeholder="选择HTTP方法">
            <el-option label="GET" value="GET"></el-option>
            <el-option label="POST" value="POST"></el-option>
            <el-option label="PUT" value="PUT"></el-option>
            <el-option label="DELETE" value="DELETE"></el-option>
            <el-option label="PATCH" value="PATCH"></el-option>
            <el-option label="HEAD" value="HEAD"></el-option>
            <el-option label="OPTIONS" value="OPTIONS"></el-option>
          </el-select>
          <span class="help-text">当请求使用此HTTP方法时触发断点</span>
        </el-form-item>
        
        <!-- 响应状态码断点 -->
        <el-form-item label="响应状态码断点">
          <el-switch v-model="config.status.enabled"></el-switch>
        </el-form-item>
        <el-form-item v-if="config.status.enabled" label="状态码">
          <el-input-number 
            v-model="config.status.value" 
            :min="100" 
            :max="599"
            placeholder="例如: 404">
          </el-input-number>
          <span class="help-text">当响应状态码匹配时触发断点</span>
        </el-form-item>
        
        <!-- UI加粗设置 -->
        <el-form-item label="UI加粗显示">
          <el-switch v-model="config.boldURI.enabled"></el-switch>
        </el-form-item>
        <el-form-item v-if="config.boldURI.enabled" label="URI包含">
          <el-input 
            v-model="config.boldURI.value" 
            placeholder="例如: /important"
            clearable>
          </el-input>
          <span class="help-text">包含此字符串的URI将在会话列表中加粗显示</span>
        </el-form-item>
      </el-form>
      
      <div class="breakpoint-summary" v-if="hasEnabledBreakpoints">
        <h4>当前断点设置：</h4>
        <ul>
          <li v-if="config.requestURI.enabled && config.requestURI.value">
            <el-tag type="danger">请求断点</el-tag>
            <span>URI包含: {{ config.requestURI.value }}</span>
          </li>
          <li v-if="config.responseURI.enabled && config.responseURI.value">
            <el-tag type="warning">响应断点</el-tag>
            <span>URI包含: {{ config.responseURI.value }}</span>
          </li>
          <li v-if="config.method.enabled && config.method.value">
            <el-tag type="info">方法断点</el-tag>
            <span>HTTP方法: {{ config.method.value }}</span>
          </li>
          <li v-if="config.status.enabled && config.status.value">
            <el-tag type="primary">状态断点</el-tag>
            <span>状态码: {{ config.status.value }}</span>
          </li>
          <li v-if="config.boldURI.enabled && config.boldURI.value">
            <el-tag>UI加粗</el-tag>
            <span>URI包含: {{ config.boldURI.value }}</span>
          </li>
        </ul>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'BreakpointConfig',
  data() {
    return {
      config: {
        requestURI: {
          enabled: false,
          value: ''
        },
        responseURI: {
          enabled: false,
          value: ''
        },
        method: {
          enabled: false,
          value: ''
        },
        status: {
          enabled: false,
          value: null
        },
        boldURI: {
          enabled: false,
          value: ''
        }
      }
    }
  },
  computed: {
    hasEnabledBreakpoints() {
      return Object.values(this.config).some(bp => bp.enabled && bp.value)
    }
  },
  methods: {
    getConfig() {
      return JSON.parse(JSON.stringify(this.config))
    },
    
    resetConfig() {
      this.config = {
        requestURI: { enabled: false, value: '' },
        responseURI: { enabled: false, value: '' },
        method: { enabled: false, value: '' },
        status: { enabled: false, value: null },
        boldURI: { enabled: false, value: '' }
      }
    }
  }
}
</script>

<style scoped>
.breakpoint-config {
  max-width: 800px;
}

.help-text {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
  display: block;
  margin-top: 5px;
}

.breakpoint-summary {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.breakpoint-summary h4 {
  margin: 0 0 15px 0;
  color: #606266;
}

.breakpoint-summary ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.breakpoint-summary li {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.breakpoint-summary li span {
  margin-left: 10px;
  color: #606266;
}
</style>
