<a name="4.1.3"></a>
## [4.1.3](https://github.com/vuejs/vue-style-loader/compare/v4.0.1...v4.1.3) (2021-03-03)


### Bug Fixes

* also support passed ES modules from `css-loader` in addition to CommonJS format ([#47](https://github.com/vuejs/vue-style-loader/issues/47)) ([8b584bd](https://github.com/vuejs/vue-style-loader/commit/8b584bd))
* es module interop in HMR code ([8bc2fe3](https://github.com/vuejs/vue-style-loader/commit/8bc2fe3))
* fix addStyleShadow when same style object is inserted multiple times ([12846a6](https://github.com/vuejs/vue-style-loader/commit/12846a6))
* fix inconsistent hashes between Windows and POSIX systems ([#28](https://github.com/vuejs/vue-style-loader/issues/28)) ([cf8b6e8](https://github.com/vuejs/vue-style-loader/commit/cf8b6e8))


### Features

* support vue-loader 15 ([0c7ee9d](https://github.com/vuejs/vue-style-loader/commit/0c7ee9d))



<a name="4.1.2"></a>
## [4.1.2](https://github.com/vuejs/vue-style-loader/compare/v4.1.1...v4.1.2) (2018-08-13)


### Bug Fixes

* fix inconsistent hashes between Windows and POSIX systems ([#28](https://github.com/vuejs/vue-style-loader/issues/28)) ([cf8b6e8](https://github.com/vuejs/vue-style-loader/commit/cf8b6e8))



<a name="4.1.1"></a>
## [4.1.1](https://github.com/vuejs/vue-style-loader/compare/v4.1.0...v4.1.1) (2018-07-17)


### Bug Fixes

* fix addStyleShadow when same style object is inserted multiple times ([12846a6](https://github.com/vuejs/vue-style-loader/commit/12846a6))



<a name="4.1.0"></a>
# [4.1.0](https://github.com/vuejs/vue-style-loader/compare/v4.0.2...v4.1.0) (2018-03-20)


### Features

* support vue-loader 15 ([0c7ee9d](https://github.com/vuejs/vue-style-loader/commit/0c7ee9d))



<a name="4.0.2"></a>
## [4.0.2](https://github.com/vuejs/vue-style-loader/compare/v4.0.1...v4.0.2) (2018-02-13)



<a name="4.0.1"></a>
## [4.0.1](https://github.com/vuejs/vue-style-loader/compare/v4.0.0...v4.0.1) (2018-01-31)


### Bug Fixes

* typo ([00087b7](https://github.com/vuejs/vue-style-loader/commit/00087b7))



<a name="4.0.0"></a>
# [4.0.0](https://github.com/vuejs/vue-style-loader/compare/v3.1.1...v4.0.0) (2018-01-31)


### Features

* shadowMode ([94737e5](https://github.com/vuejs/vue-style-loader/commit/94737e5))
* use ESM for runtime files ([18d0ae4](https://github.com/vuejs/vue-style-loader/commit/18d0ae4))



<a name="3.1.1"></a>
## [3.1.1](https://github.com/vuejs/vue-style-loader/compare/v3.1.0...v3.1.1) (2018-01-24)



<a name="3.1.0"></a>
# [3.1.0](https://github.com/vuejs/vue-style-loader/compare/v3.0.3...v3.1.0) (2018-01-24)


### Features

* add `ssrId` option for rendering ssr id on client ([5281305](https://github.com/vuejs/vue-style-loader/commit/5281305))
