<template>
  <div class="script-preview">
    <el-card>
      <div slot="header">
        <span>脚本预览</span>
        <el-button 
          style="float: right; padding: 3px 0" 
          type="text" 
          @click="copyScript"
          :disabled="!generatedScript">
          复制脚本
        </el-button>
      </div>
      
      <div v-if="!generatedScript" class="no-script">
        <el-alert
          title="暂无生成的脚本"
          description="请先配置规则，然后点击"生成脚本"按钮"
          type="info"
          :closable="false">
        </el-alert>
      </div>
      
      <div v-else class="script-content">
        <div class="script-info">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-statistic title="脚本行数" :value="scriptLines"></el-statistic>
            </el-col>
            <el-col :span="8">
              <el-statistic title="文件大小" :value="scriptSize" suffix="字节"></el-statistic>
            </el-col>
            <el-col :span="8">
              <el-statistic title="生成时间" :value="generateTime"></el-statistic>
            </el-col>
          </el-row>
        </div>
        
        <el-divider></el-divider>
        
        <div class="code-container">
          <pre class="code-block"><code>{{ generatedScript }}</code></pre>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import ScriptGenerator from '../utils/ScriptGenerator'

export default {
  name: 'ScriptPreview',
  props: {
    config: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      generatedScript: '',
      generateTime: ''
    }
  },
  computed: {
    scriptLines() {
      return this.generatedScript ? this.generatedScript.split('\n').length : 0
    },
    
    scriptSize() {
      return this.generatedScript ? new Blob([this.generatedScript]).size : 0
    }
  },
  watch: {
    config: {
      handler() {
        this.generateScript()
      },
      deep: true
    }
  },
  methods: {
    generateScript() {
      try {
        this.generatedScript = ScriptGenerator.generate(this.config)
        this.generateTime = new Date().toLocaleString()
      } catch (error) {
        console.error('生成脚本失败:', error)
        this.generatedScript = ''
      }
    },
    
    copyScript() {
      if (!this.generatedScript) {
        this.$message.warning('没有可复制的脚本')
        return
      }
      
      // 创建临时文本区域
      const textarea = document.createElement('textarea')
      textarea.value = this.generatedScript
      document.body.appendChild(textarea)
      textarea.select()
      
      try {
        document.execCommand('copy')
        this.$message.success('脚本已复制到剪贴板')
      } catch (err) {
        this.$message.error('复制失败，请手动复制')
      }
      
      document.body.removeChild(textarea)
    }
  },
  mounted() {
    this.generateScript()
  }
}
</script>

<style scoped>
.script-preview {
  max-width: 100%;
}

.no-script {
  text-align: center;
  padding: 40px;
}

.script-content {
  max-height: 600px;
  overflow-y: auto;
}

.script-info {
  margin-bottom: 20px;
}

.code-container {
  background-color: #f5f5f5;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: auto;
  max-height: 400px;
}

.code-block {
  margin: 0;
  padding: 15px;
  font-family: 'Courier New', Consolas, monospace;
  font-size: 12px;
  line-height: 1.5;
  color: #2c3e50;
  background-color: transparent;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* 代码高亮样式 */
.code-block {
  background-color: #f8f8f8;
}

/* 滚动条样式 */
.code-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.code-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.code-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.code-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
