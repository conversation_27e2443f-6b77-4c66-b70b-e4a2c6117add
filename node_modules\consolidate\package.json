{"name": "consolidate", "description": "Template engine consolidation library", "version": "0.15.1", "homepage": "https://github.com/tj/consolidate.js", "author": "<PERSON><PERSON> <<EMAIL>>", "repository": "tj/consolidate.js", "bugs": {"url": "https://github.com/tj/consolidate.js/issues"}, "license": "MIT", "files": ["index.js", "lib"], "main": "index.js", "engines": {"node": ">= 0.10.0"}, "scripts": {"lint": "eslint .", "pretest": "npm run lint", "test": "mocha"}, "dependencies": {"bluebird": "^3.1.1"}, "devDependencies": {"arc-templates": "^0.5.1", "atpl": ">=0.7.6", "babel-core": "^6.7.6", "babel-preset-react": "^6.5.0", "bracket-template": "^1.1.4", "coffee-script": "^1.11.1", "dot": "^1.0.1", "dust": "^0.3.0", "dustjs-helpers": "^1.1.1", "dustjs-linkedin": "^2.7.2", "eco": "^1.1.0-rc-3", "ect": "^0.5.9", "ejs": "^2.3.4", "eslint": "^3.7.1", "eslint-config-standard": "^6.2.0", "eslint-plugin-promise": "^3.3.1", "eslint-plugin-standard": "^2.0.1", "haml-coffee": "^1.4.0", "hamlet": "^0.3.3", "hamljs": "^0.6.1", "handlebars": "^4.0.5", "hogan.js": "^3.0.2", "htmling": "^0.0.7", "jade": "^1.9.1", "jazz": "^0.0.18", "jqtpl": "~1.1.0", "just": "^0.1.8", "liquid-node": "^2.6.1", "liquor": "^0.0.5", "lodash": "^4.0.0", "marko": "^3.12.0", "mocha": "^3.1.2", "mote": "^0.2.0", "mustache": "^2.2.1", "nunjucks": "^3.0.0", "plates": "~0.4.8", "pug": "^2.0.0-beta6", "qejs": "^3.0.5", "ractive": "^0.8.4", "react": "^15.3.2", "react-dom": "^15.3.2", "should": "*", "slm": "^0.5.0", "swig-templates": "^2.0.2", "swig": "^1.4.1", "teacup": "^2.0.0", "templayed": ">=0.2.3", "tinyliquid": "^0.2.30", "toffee": "^0.1.12", "twig": "^0.10.0", "underscore": "^1.3.3", "vash": "^0.12.2", "walrus": "^0.10.1", "whiskers": "^0.4.0", "velocityjs": "^0.8.2"}, "keywords": ["engine", "template", "view"]}