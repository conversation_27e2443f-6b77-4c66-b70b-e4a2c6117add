<template>
  <div class="url-replace-config">
    <el-card>
      <div slot="header">
        <span>URL替换规则</span>
      </div>
      
      <el-form :model="config" label-width="120px">
        <el-form-item label="启用URL替换">
          <el-switch v-model="config.enabled"></el-switch>
          <span class="help-text">启用后将替换URL中的指定字符串</span>
        </el-form-item>
        
        <div v-if="config.enabled">
          <el-form-item label="替换规则">
            <el-button type="primary" size="small" @click="addRule">添加规则</el-button>
          </el-form-item>
          
          <div v-for="(rule, index) in config.rules" :key="index" class="rule-item">
            <el-row :gutter="10">
              <el-col :span="10">
                <el-input 
                  v-model="rule.from" 
                  placeholder="要替换的字符串"
                  size="small">
                  <template slot="prepend">查找</template>
                </el-input>
              </el-col>
              <el-col :span="10">
                <el-input 
                  v-model="rule.to" 
                  placeholder="替换为"
                  size="small">
                  <template slot="prepend">替换</template>
                </el-input>
              </el-col>
              <el-col :span="4">
                <el-button 
                  type="danger" 
                  size="small" 
                  icon="el-icon-delete"
                  @click="removeRule(index)">
                </el-button>
              </el-col>
            </el-row>
            <div class="rule-example">
              <span class="example-label">示例:</span>
              <span class="example-text">
                {{ rule.from ? `将 "${rule.from}" 替换为 "${rule.to || '[空]'}"` : '请输入要替换的字符串' }}
              </span>
            </div>
          </div>
          
          <div v-if="config.rules.length === 0" class="no-rules">
            <el-alert
              title="暂无替换规则"
              description="点击"添加规则"按钮来创建URL替换规则"
              type="info"
              :closable="false">
            </el-alert>
          </div>
        </div>
      </el-form>
      
      <div class="rules-preview" v-if="config.enabled && config.rules.length > 0">
        <h4>替换规则预览：</h4>
        <el-table :data="validRules" size="small" border>
          <el-table-column prop="from" label="查找字符串" width="200"></el-table-column>
          <el-table-column prop="to" label="替换为" width="200"></el-table-column>
          <el-table-column label="说明">
            <template slot-scope="scope">
              <span>URL中的 "{{ scope.row.from }}" 将被替换为 "{{ scope.row.to || '[删除]' }}"</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'UrlReplaceConfig',
  data() {
    return {
      config: {
        enabled: false,
        rules: []
      }
    }
  },
  computed: {
    validRules() {
      return this.config.rules.filter(rule => rule.from && rule.from.trim())
    }
  },
  methods: {
    getConfig() {
      return {
        enabled: this.config.enabled,
        rules: this.validRules
      }
    },
    
    resetConfig() {
      this.config = {
        enabled: false,
        rules: []
      }
    },
    
    addRule() {
      this.config.rules.push({
        from: '',
        to: ''
      })
    },
    
    removeRule(index) {
      this.config.rules.splice(index, 1)
    }
  },
  mounted() {
    // 默认添加一个空规则作为示例
    if (this.config.rules.length === 0) {
      this.addRule()
    }
  }
}
</script>

<style scoped>
.url-replace-config {
  max-width: 900px;
}

.help-text {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}

.rule-item {
  margin-bottom: 15px;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;
}

.rule-example {
  margin-top: 8px;
  font-size: 12px;
}

.example-label {
  color: #909399;
  font-weight: bold;
}

.example-text {
  color: #606266;
  margin-left: 5px;
}

.no-rules {
  margin-top: 20px;
}

.rules-preview {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.rules-preview h4 {
  margin: 0 0 15px 0;
  color: #606266;
}
</style>
