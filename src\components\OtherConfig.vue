<template>
  <div class="other-config">
    <el-card>
      <div slot="header">
        <span>其他配置选项</span>
      </div>
      
      <el-form :model="config" label-width="150px">
        <el-form-item label="自定义菜单">
          <el-switch v-model="config.customMenu.enabled"></el-switch>
          <span class="help-text">添加自定义快速链接菜单</span>
        </el-form-item>
        
        <div v-if="config.customMenu.enabled">
          <el-form-item label="菜单名称">
            <el-input v-model="config.customMenu.name" placeholder="例如: 常用链接"></el-input>
          </el-form-item>
          
          <el-form-item label="菜单项">
            <el-button type="primary" size="small" @click="addMenuItem">添加菜单项</el-button>
          </el-form-item>
          
          <div v-for="(item, index) in config.customMenu.items" :key="index" class="menu-item">
            <el-row :gutter="10">
              <el-col :span="8">
                <el-input 
                  v-model="item.title" 
                  placeholder="菜单项标题"
                  size="small">
                </el-input>
              </el-col>
              <el-col :span="12">
                <el-input 
                  v-model="item.url" 
                  placeholder="链接URL"
                  size="small">
                </el-input>
              </el-col>
              <el-col :span="4">
                <el-button 
                  type="danger" 
                  size="small" 
                  icon="el-icon-delete"
                  @click="removeMenuItem(index)">
                </el-button>
              </el-col>
            </el-row>
          </div>
        </div>
        
        <el-divider></el-divider>
        
        <el-form-item label="自定义列">
          <el-switch v-model="config.customColumn.enabled"></el-switch>
          <span class="help-text">在会话列表中添加自定义列</span>
        </el-form-item>
        
        <div v-if="config.customColumn.enabled">
          <el-form-item label="列名称">
            <el-input v-model="config.customColumn.name" placeholder="例如: 服务器"></el-input>
          </el-form-item>
          
          <el-form-item label="列宽度">
            <el-input-number v-model="config.customColumn.width" :min="50" :max="300"></el-input-number>
            <span class="help-text">像素</span>
          </el-form-item>
          
          <el-form-item label="显示内容">
            <el-select v-model="config.customColumn.content" placeholder="选择显示内容">
              <el-option label="响应服务器头" value="@response.server"></el-option>
              <el-option label="请求方法" value="@request.method"></el-option>
              <el-option label="响应状态码" value="@response.status"></el-option>
              <el-option label="内容类型" value="@response.content-type"></el-option>
            </el-select>
          </el-form-item>
        </div>
        
        <el-divider></el-divider>
        
        <el-form-item label="全局热键">
          <el-switch v-model="config.hotkey.enabled"></el-switch>
          <span class="help-text">注册全局热键</span>
        </el-form-item>
        
        <div v-if="config.hotkey.enabled">
          <el-form-item label="热键组合">
            <el-select v-model="config.hotkey.modifier" placeholder="选择修饰键">
              <el-option label="Windows键" value="Windows"></el-option>
              <el-option label="Ctrl键" value="Control"></el-option>
              <el-option label="Alt键" value="Alt"></el-option>
            </el-select>
          </el-form-item>
          
          <el-form-item label="按键">
            <el-input v-model="config.hotkey.key" placeholder="例如: G" maxlength="1"></el-input>
          </el-form-item>
          
          <el-form-item label="执行动作">
            <el-input v-model="config.hotkey.action" placeholder="例如: screenshot"></el-input>
          </el-form-item>
        </div>
        
        <el-divider></el-divider>
        
        <el-form-item label="启动时显示消息">
          <el-switch v-model="config.bootMessage.enabled"></el-switch>
          <span class="help-text">Fiddler启动时显示自定义消息</span>
        </el-form-item>
        
        <div v-if="config.bootMessage.enabled">
          <el-form-item label="消息内容">
            <el-input 
              v-model="config.bootMessage.message" 
              type="textarea"
              :rows="2"
              placeholder="输入启动时要显示的消息">
            </el-input>
          </el-form-item>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'OtherConfig',
  data() {
    return {
      config: {
        customMenu: {
          enabled: false,
          name: '',
          items: []
        },
        customColumn: {
          enabled: false,
          name: '',
          width: 100,
          content: ''
        },
        hotkey: {
          enabled: false,
          modifier: '',
          key: '',
          action: ''
        },
        bootMessage: {
          enabled: false,
          message: ''
        }
      }
    }
  },
  methods: {
    getConfig() {
      return JSON.parse(JSON.stringify(this.config))
    },
    
    resetConfig() {
      this.config = {
        customMenu: { enabled: false, name: '', items: [] },
        customColumn: { enabled: false, name: '', width: 100, content: '' },
        hotkey: { enabled: false, modifier: '', key: '', action: '' },
        bootMessage: { enabled: false, message: '' }
      }
    },
    
    addMenuItem() {
      this.config.customMenu.items.push({
        title: '',
        url: ''
      })
    },
    
    removeMenuItem(index) {
      this.config.customMenu.items.splice(index, 1)
    }
  }
}
</script>

<style scoped>
.other-config {
  max-width: 800px;
}

.help-text {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}

.menu-item {
  margin-bottom: 10px;
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;
}
</style>
