This is an internal package used by `vue-loader` and `vueify`. It processes the raw render functions generated by `vue-template-compiler` to:

1. add support to ES2015 features in template expressions via Buble. (see [supported features here](https://buble.surge.sh/guide/#supported-features)).

**Note:** since version 1.8.0, object rest spread usage inside templates are transpiled to `Object.assign` calls by default. This means if you need to support IE, you will need to polyfill `Object.assign`. (Latest version of Vue CLI will do this for you).

2. remove the `with` block inside render functions to make it strict-mode compliant. This is performed only at build time so that the base template compiler can be extremely small and lightweight.

The buble implementation is built from a fork at https://github.com/yyx990803/buble
